import DashboardStatus from "@/app/ui/dashboard/overview"
import NotificationBar from "@/app/ui/dashboard/notifications"
import MainReporting from "@/app/ui/dashboard/reports-holder"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { useEffect, useState } from 'react'
 
export function DashboardTabs() {
    const [logbooks, setLogbooks] = useState<any>([])

    return (
        <Tabs defaultValue="overview">
            <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
            </TabsList>
            <TabsContent value="overview">
                <DashboardStatus />
            </TabsContent>
            <TabsContent value="reports">
                <MainReporting />
            </TabsContent>
            <TabsContent value="notifications">
                <NotificationBar />
            </TabsContent>
        </Tabs>
    )
}