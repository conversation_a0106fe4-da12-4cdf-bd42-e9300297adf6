'use client'

import * as React from 'react'
import { cn } from '@/app/lib/utils'

export interface TextareaProps
    extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    /**
     * Whether the textarea should resize automatically based on content
     */
    autoResize?: boolean
    /**
     * Maximum height for auto-resizing textarea (in pixels)
     */
    maxAutoHeight?: number
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
    ({ className, autoResize, maxAutoHeight = 400, ...props }, ref) => {
        const textareaRef = React.useRef<HTMLTextAreaElement | null>(null)
        const mergedRef = React.useMemo(() => {
            return (node: HTMLTextAreaElement | null) => {
                textareaRef.current = node
                if (typeof ref === 'function') {
                    ref(node)
                } else if (ref) {
                    ;(
                        ref as React.MutableRefObject<HTMLTextAreaElement | null>
                    ).current = node
                }
            }
        }, [ref])

        // Auto-resize functionality
        const adjustHeight = React.useCallback(() => {
            const textarea = textareaRef.current
            if (!textarea || !autoResize) return

            // Reset height to auto to get the correct scrollHeight
            textarea.style.height = 'auto'

            // Calculate new height (limit to maxAutoHeight)
            const newHeight = Math.min(textarea.scrollHeight, maxAutoHeight)
            textarea.style.height = `${newHeight}px`
        }, [autoResize, maxAutoHeight])

        // Adjust height on content change
        React.useEffect(() => {
            if (autoResize) {
                adjustHeight()
                // Add resize observer to handle container size changes
                const resizeObserver = new ResizeObserver(adjustHeight)
                if (textareaRef.current) {
                    resizeObserver.observe(textareaRef.current)
                }
                return () => resizeObserver.disconnect()
            }
        }, [autoResize, adjustHeight, props.value, props.defaultValue])

        return (
            <textarea
                className={cn(
                    'flex min-h-[172px] w-full rounded-md border border-border bg-accent/0 px-3 py-2 text-base text-input shadow-sm transition-colors',
                    'placeholder:text-neutral-400',
                    'focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-curious-blue-400',
                    'hover:border-curious-blue-400',
                    'disabled:cursor-not-allowed disabled:opacity-50',
                    'resize-y',
                    className,
                )}
                ref={mergedRef}
                onChange={(e) => {
                    props.onChange?.(e)
                    if (autoResize) {
                        adjustHeight()
                    }
                }}
                {...props}
            />
        )
    },
)

Textarea.displayName = 'Textarea'

export { Textarea }
