'use client'

import * as React from 'react'
import { useRef } from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { ArrowLeft, Loader2, type LucideIcon } from 'lucide-react'
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ontent,
    Too<PERSON><PERSON><PERSON><PERSON>ider,
    Too<PERSON><PERSON>Trigger,
} from '@/components/ui/tooltip'
import { cn } from '@/app/lib/utils'
import { useContainerQuery } from '@/hooks/use-container-query'
import { useMergedRefs } from '@reactuses/core'

/* -------------------------------------------------------------------------- */
/* Variants                                                                   */
/* -------------------------------------------------------------------------- */

export const buttonVariants = cva(
    'inline-flex items-center justify-center rounded-[6px] gap-[8.5px] whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0',
    {
        variants: {
            variant: {
                primary:
                    'h-11 py-3 border rounded-[6px] bg-primary border-primary text-primary-foreground shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-card hover:text-accent-foreground hover:border-border',
                back: 'bg-transparent text-curious-blue-400 group gap-[5px] transition-all duration-300',
                destructive:
                    'text-destructive border bg-destructive-foreground items-center justify-center border-destructive rounded-[6px] hover:bg-cinnabar-100',
                destructiveFill:
                    'h-11 py-3 border rounded-[6px] bg-destructive border-destructive text-primary-foreground shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-card hover:text-destructive hover:border-border',
                outline:
                    'border border-border bg-card hover:bg-accent hover:text-accent-foreground font-normal flex-1 [&_svg]:size-auto text-input',
                primaryOutline:
                    'border border-border bg-card hover:bg-accent hover:text-accent-foreground text-input font-normal',
                secondary:
                    'bg-secondary text-foreground shadow-sm hover:bg-secondary/80',
                warning:
                    'hover:bg-fire-bush-100 border border-fire-bush-700 hover:text-fire-bush-700 text-fire-bush-700',
                ghost: 'hover:bg-card hover:text-input',
                link: 'text-primary underline-offset-4 hover:underline',
                text: 'bg-transparent hover:bg-transparent shadow-none text-foreground p-0',
                info: 'bg-curious-blue-600 text-white hover:bg-curious-blue-700',
            },
            size: {
                default: 'h-11 xs:px-2.5 px-3  py-3',
                sm: 'h-8 rounded-md px-3 text-xs',
                md: 'h-11 px-3 px-2.5  py-3',
                lg: 'h-12 xs:px-6 py-3 text-base rounded-md',
                icon: 'size-10 p-2',
            },
        },
        defaultVariants: {
            variant: 'primary',
            size: 'default',
        },
    },
)

/* -------------------------------------------------------------------------- */
/* Types                                                                      */
/* -------------------------------------------------------------------------- */

type IconType = React.ReactNode | LucideIcon

type ResponsiveButtonProps = {
    responsive: true
    iconLeft?: IconType
    iconRight?: IconType
}

type NonResponsiveButtonProps = {
    responsive?: false
    iconLeft?: IconType
    iconRight?: IconType
}

interface ButtonBaseProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean
    isLoading?: boolean
    iconLeft?: IconType
    iconRight?: IconType
    tooltip?: string
    iconOnly?: boolean
    iconSize?: number
    extraSpace?: number
    asInput?: boolean
}

export type ButtonProps = ButtonBaseProps &
    (ResponsiveButtonProps | NonResponsiveButtonProps)

/* -------------------------------------------------------------------------- */
/* Component                                                                  */
/* -------------------------------------------------------------------------- */

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    (
        {
            className,
            variant,
            size,
            asChild = false,
            isLoading = false,
            iconLeft,
            iconRight,
            iconSize = 20,
            tooltip,
            iconOnly = false,
            responsive = false,
            extraSpace = 16,
            children,
            asInput = false,
            ...props
        },
        ref,
    ) => {
        /* asChild – passthrough -------------------------------------------------- */
        if (asChild) {
            const Comp = Slot
            return (
                <Comp
                    className={cn(buttonVariants({ variant, size }), className)}
                    ref={ref}
                    {...props}>
                    {children}
                </Comp>
            )
        }

        /* Default iconLeft for back variant ------------------------------------- */
        const resolvedIconLeft =
            variant === 'back' && !iconLeft ? ArrowLeft : iconLeft

        /* Responsive logic ------------------------------------------------------- */
        const containerRef = useRef<HTMLButtonElement>(null)
        const contentRef = useRef<HTMLSpanElement>(null)
        const mergedRef = useMergedRefs<HTMLButtonElement>(ref, containerRef)

        const contentFits = useContainerQuery(
            contentRef,
            containerRef,
            extraSpace,
        )
        const shouldHideLabel = responsive && !contentFits && !iconOnly

        /* Tooltip logic ---------------------------------------------------------- */
        const needsTooltip = tooltip || (shouldHideLabel && children)
        const tooltipText =
            tooltip || (typeof children === 'string' ? children : '')

        /* Base classes ----------------------------------------------------------- */
        const baseClasses = cn(
            buttonVariants({ variant, size }),
            iconOnly &&
                'flex items-center border-none size-fit [&_svg]:size-auto p-0 hover:bg-transparent shadow-none justify-center',
            shouldHideLabel && 'px-3',
            variant === 'text' && 'p-0 shadow-none',
            variant === 'back' && 'gap-[5px]',
            // Prevent right-hand icon overlap in combobox button when it scrolls
            asInput && !iconOnly && 'overflow-hidden min-w-0',
            'will-change-transform will-change-width will-change-padding transform-gpu hover:transition-colors hover:ease-out hover:duration-300',
            className,
        )

        /* ----------------------------------------------------------------------- */
        /* Render                                                                  */
        /* ----------------------------------------------------------------------- */
        const buttonContent = (
            <>
                {isLoading && <Loader2 className="size-5 animate-spin" />}

                {/* Left icon ---------------------------------------------------------- */}
                {resolvedIconLeft && !isLoading && (
                    <span
                        className={
                            variant === 'back'
                                ? 'relative group-hover:-translate-x-[5px] w-fit transition-transform ease-out duration-300'
                                : 'flex-shrink-0 w-fit'
                        }>
                        {React.isValidElement(resolvedIconLeft)
                            ? resolvedIconLeft
                            : React.createElement(resolvedIconLeft as any, {
                                  size: iconSize,
                              })}
                    </span>
                )}

                {/* Label -------------------------------------------------------------- */}
                {children && !iconOnly && (
                    <span
                        ref={contentRef}
                        className={cn(
                            shouldHideLabel ? 'sr-only' : 'flex-shrink-0',
                            asInput &&
                                'flex-1 min-w-0 truncate whitespace-nowrap',
                        )}>
                        {children}
                    </span>
                )}

                {/* Right icon --------------------------------------------------------- */}
                {iconRight && !isLoading && (
                    <span className="flex-shrink-0 w-fit">
                        {React.isValidElement(iconRight)
                            ? iconRight
                            : React.createElement(iconRight as any, {
                                  size: iconSize,
                              })}
                    </span>
                )}
            </>
        )

        /* Tooltip wrapper -------------------------------------------------------- */
        if (needsTooltip) {
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger
                            ref={mergedRef}
                            className={baseClasses}
                            disabled={isLoading || props.disabled}
                            {...props}>
                            {buttonContent}
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{tooltipText}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            )
        }

        return (
            <button
                ref={mergedRef}
                className={baseClasses}
                disabled={isLoading || props.disabled}
                {...props}>
                {buttonContent}
            </button>
        )
    },
)
Button.displayName = 'Button'
