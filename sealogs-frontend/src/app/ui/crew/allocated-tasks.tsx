'use client'

import Link from 'next/link'
import { isEmpty } from 'lodash'
import { usePathname, useSearchParams } from 'next/navigation'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { Button } from '@/components/ui/button'
import {
    <PERSON>over,
    PopoverContent,
    PopoverTrigger,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { useState, useEffect } from 'react'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import VesselIcon from '../vessels/vesel-icon'
import { useLazyQuery } from '@apollo/client'
import { GET_INVENTORY_BY_IDS } from '@/app/lib/graphQL/query/GET_INVENTORY_BY_IDS'
import { MessageSquareText } from 'lucide-react'
import { TableSkeleton } from '@/components/skeletons'

type Task = {
    id: string
    name: string
    basicComponentID: string
    comments: string | null
    assignedToID: string
    expires: string // e.g., "3/4/2025"
    status: string // e.g., "Open"
    startDate: string // e.g., "2025-06-06 00:00:00"
    isOverDue: {
        status: string // e.g., "High"
        days: string // e.g., "97 days ago"
    }
    basicComponent: {
        __typename: string // e.g., "Vessel"
        id: string
        title: string
    }
    isCompleted: string // possibly "0" or "1" or another enum-like string
    inventoryID: string
    description?: string // Add optional description field
}

const CrewAllocatedTasks = ({ taskList }: { taskList?: Task[] }) => {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [filteredTaskList, setFilteredTaskList] = useState(taskList)
    const { getVesselWithIcon } = useVesselIconData()
    const [inventories, setInventories] = useState<any>()
    const [isLoading, setIsLoading] = useState(true)
    const [filters, setFilters] = useState<{
        vessel: any
        status: any
        keyword: any
    }>({
        vessel: null,
        status: null,
        keyword: null,
    })
    const inventoryIDs = Array.from(
        new Set(
            (filteredTaskList || [])
                .map((task: any) => +task.inventoryID)
                .filter(Boolean),
        ),
    )

    const [queryInventoriesByIds] = useLazyQuery(GET_INVENTORY_BY_IDS, {
        onCompleted: (response: any) => {
            const data = response.readInventories.nodes
            if (data) {
                setInventories(data)
            }
        },
    })

    useEffect(() => {
        if (isLoading) {
            loadTaskList()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadTaskList = async () => {
        await queryInventoriesByIds({
            variables: {
                id: inventoryIDs,
            },
        })
    }

    // Update filtered list when taskList changes
    useEffect(() => {
        setFilteredTaskList(taskList)
    }, [taskList])

    // Apply all filters whenever filters or taskList changes
    useEffect(() => {
        if (!taskList) return

        let filtered = [...taskList]

        // Apply vessel filter - following the same pattern as maintenance list
        if (filters.vessel) {
            // Handle both single and multi-select vessel filtering
            let vesselIds: string[] = []
            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {
                vesselIds = filters.vessel.map((item: any) =>
                    String(item.value),
                )
            } else if (filters.vessel && !Array.isArray(filters.vessel)) {
                vesselIds = [String(filters.vessel.value)]
            }

            if (vesselIds.length > 0) {
                filtered = filtered.filter((task: any) => {
                    const taskVesselId = String(task?.basicComponent?.id)
                    return vesselIds.includes(taskVesselId)
                })
            }
        }

        // Apply status filter
        if (filters.status) {
            filtered = filtered.filter(
                (task: any) => task?.status === filters.status.value,
            )
        }

        // Apply keyword filter
        if (
            filters.keyword &&
            filters.keyword.value &&
            filters.keyword.value.trim()
        ) {
            const keyword = filters.keyword.value.toLowerCase().trim()
            filtered = filtered.filter((task: any) => {
                // Safely get text content, handling null/undefined and HTML
                const getName = () => (task?.name || '').toLowerCase()
                const getDescription = () =>
                    (task?.description || '').toLowerCase()
                const getComments = () => {
                    if (!task?.comments) return ''
                    // Strip HTML tags if present and convert to lowercase
                    return task.comments.replace(/<[^>]*>/g, '').toLowerCase()
                }

                const nameMatch = getName().includes(keyword)
                const descMatch = getDescription().includes(keyword)
                const commentMatch = getComments().includes(keyword)
                return nameMatch || descMatch || commentMatch
            })
        }

        setFilteredTaskList(filtered)
    }, [taskList, filters])

    // Handle filter changes
    const handleFilterOnChange = ({ type, data }: any) => {
        setFilters((prev) => ({
            ...prev,
            [type]: data,
        }))
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<Task, any>[] = [
        {
            accessorKey: 'name',
            header: 'Task',
            cellAlignment: 'left',
            cell: ({ row }) => {
                const task: Task = row.original

                return (
                    <div>
                        <div className="flex items-center justify-between">
                            <span className="text-foreground flex items-center">
                                <Link
                                    href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                    className="focus:outline-none">
                                    {task.name}
                                </Link>
                            </span>
                            <div className="w-14 flex items-center pl-1">
                                {task.comments !== undefined &&
                                    task.comments !== null && (
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="outline-none px-1">
                                                    <MessageSquareText className="w-5 h-5" />
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-64">
                                                <div className="leading-loose">
                                                    {task.comments}
                                                </div>
                                            </PopoverContent>
                                        </Popover>
                                    )}
                            </div>
                        </div>
                        {task.description && (
                            <div className="mt-1 text-sm text-muted-foreground">
                                {task.description}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'basicComponent',
            header: 'Vessel',
            cellAlignment: 'center',
            cell: ({ row }) => {
                const task: Task = row.original

                const vesselWithIcon = getVesselWithIcon(
                    task.basicComponent?.id,
                    task.basicComponent,
                )
                return (
                    <>
                        {task.basicComponent?.id !== '0' ? (
                            <Tooltip key={task.basicComponent.id}>
                                <TooltipTrigger>
                                    <div className="min-w-fit">
                                        <VesselIcon vessel={vesselWithIcon} />
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    {task.basicComponent.title}
                                </TooltipContent>
                            </Tooltip>
                        ) : null}
                    </>
                )
            },
        },
        {
            accessorKey: 'inventoryID',
            header: 'Inventory Item',
            cellAlignment: 'center',
            cell: ({ row }) => {
                const task: Task = row.original
                const inventory = inventories?.find(
                    (inventory: any) => +inventory.id === +task.inventoryID,
                )

                return (
                    <div className="text-center">{inventory?.title || ''}</div>
                )
            },
        },
        {
            accessorKey: 'isOverDue',
            header: 'Status',
            cellAlignment: 'right',
            cell: ({ row }) => {
                const task: Task = row.original

                return (
                    <div className="text-right">
                        <div
                            className={`ml-3 ${
                                task.isOverDue?.status === 'High'
                                    ? 'inline-block rounded px-3 py-1 alert'
                                    : 'inline-block'
                            }`}>
                            {task.isOverDue?.status &&
                                ['High', 'Medium', 'Low'].includes(
                                    task.isOverDue.status,
                                ) &&
                                task.isOverDue.days}
                            {task.isOverDue?.status === 'Completed' &&
                                task.isOverDue.days === 'Save As Draft' &&
                                task.isOverDue.days}
                            {task.isOverDue?.status === 'Upcoming' &&
                                task.isOverDue.days}
                            {task.isOverDue?.status === 'Completed' &&
                                isEmpty(task.isOverDue.days) &&
                                task.isOverDue.status}
                            {task.isOverDue?.status === 'Completed' &&
                                !isEmpty(task.isOverDue.days) &&
                                task.isOverDue.days !== 'Save As Draft' &&
                                task.isOverDue.days}
                        </div>
                    </div>
                )
            },
        },
    ]

    return (
        <>
            {!taskList ? (
                <TableSkeleton />
            ) : (
                <DataTable
                    columns={columns}
                    data={filteredTaskList || []}
                    showToolbar={true}
                    pageSize={20}
                    onChange={handleFilterOnChange}
                />
            )}
        </>
    )
}

export default CrewAllocatedTasks
