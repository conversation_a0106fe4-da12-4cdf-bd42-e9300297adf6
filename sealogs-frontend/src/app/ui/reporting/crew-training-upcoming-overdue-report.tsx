'use client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import Link from 'next/link'
import { List, TableSkeleton } from '@/components/skeletons'
import CustomPagination from '@/components/ui/custom-pagination'
import Filter from '@/components/filter'
import { GetTrainingSessionStatus } from '@/app/lib/actions'
import { formatDate } from '@/app/helpers/dateHelper'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'

// Shadcn UI components
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Button } from '@/components/ui'
import {
    FilteredTable,
    createColumns,
    RowStatus,
} from '@/components/filteredTable'

const CrewTrainingUpcomingOverdue = ({
    memberId = 0,
    vesselId = 0,
}: {
    memberId?: number
    vesselId?: number
}) => {
    const limit = 100
    const [isLoading, setIsLoading] = useState(true)
    const [pageInfo, setPageInfo] = useState({
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
    })
    const [trainingList, setTrainingList] = useState([] as any)
    const [trainingSessionDues, setTrainingSessionDues] = useState([] as any)
    const [page, setPage] = useState(0)
    const [filter, setFilter] = useState({})
    const [vesselIdOptions, setVesselIdOptions] = useState([] as any)
    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = useState(
        [] as any,
    )
    const [trainerIdOptions, setTrainerIdOptions] = useState([] as any)
    const [crewIdOptions, setCrewIdOptions] = useState([] as any)

    const [queryTrainingList, { loading: queryTrainingListLoading }] =
        useLazyQuery(TRAINING_SESSIONS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes
                const vesselIDs = Array.from(
                    new Set(data.map((item: any) => item.vessel.id)),
                ).filter((id: any) => +id !== 0)
                const trainingTypeIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.trainingTypes.nodes.map((t: any) => t.id),
                        ),
                    ),
                )
                const trainerIDs = Array.from(
                    new Set(data.map((item: any) => item.trainerID)),
                ).filter((id: any) => +id !== 0)
                const memberIDs = Array.from(
                    new Set(
                        data.flatMap((item: any) =>
                            item.members.nodes.map((t: any) => t.id),
                        ),
                    ),
                )

                if (data) {
                    setTrainingList(data)
                    setVesselIdOptions(vesselIDs)
                    setTrainingTypeIdOptions(trainingTypeIDs)
                    setTrainerIdOptions(trainerIDs)
                    setCrewIdOptions(memberIDs)
                }
                setPageInfo(response.readTrainingSessions.pageInfo)
            },
            onError: (error: any) => {
                console.error('queryTrainingList error', error)
            },
        })
    const loadTrainingList = async (
        startPage: number = 0,
        searchFilter: any = { ...filter },
    ) => {
        await queryTrainingList({
            variables: {
                filter: searchFilter,
                offset: startPage * limit,
                limit: limit,
            },
        })
    }
    const handleNavigationClick = (newPage: any) => {
        if (newPage < 0 || newPage === page) return
        setPage(newPage)
        loadTrainingSessionDues(filter)
        loadTrainingList(newPage, filter)
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }

        if (type === 'vessel') {
            if (data) {
                searchFilter.vesselID = { eq: +data.value }
            } else {
                delete searchFilter.vesselID
            }
        }

        if (type === 'trainingType') {
            if (data) {
                searchFilter.trainingTypes = { id: { contains: +data.value } }
            } else {
                delete searchFilter.trainingTypes
            }
        }
        if (type === 'trainer') {
            if (data) {
                searchFilter.trainer = { id: { eq: +data.value } }
            } else {
                delete searchFilter.trainer
            }
        }
        if (type === 'member') {
            if (data) {
                searchFilter.members = { id: { contains: +data.value } }
            } else {
                delete searchFilter.members
            }
        }
        if (type === 'dateRange') {
            if (data.startDate && data.endDate) {
                searchFilter.date = {
                    gte: data.startDate,
                    lte: data.endDate,
                }
            } else {
                delete searchFilter.date
            }
        }
        setFilter(searchFilter)
        loadTrainingSessionDues(searchFilter)
        loadTrainingList(0, searchFilter)
    }
    const [
        readTrainingSessionDues,
        { loading: readTrainingSessionDuesLoading },
    ] = useLazyQuery(READ_TRAINING_SESSION_DUES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTrainingSessionDues.nodes
            if (data) {
                // Filter out crew members who are no longer assigned to the vessel.
                const filteredData = data.filter((item: any) =>
                    item.vessel.seaLogsMembers.nodes.some((m: any) => {
                        return m.id === item.memberID
                    }),
                )
                const dueWithStatus = filteredData.map((due: any) => {
                    return { ...due, status: GetTrainingSessionStatus(due) }
                })
                // Return only due within 7 days and overdue
                const filteredDueWithStatus = dueWithStatus.filter(
                    (item: any) => {
                        return (
                            item.status.isOverdue ||
                            (item.status.isOverdue === false &&
                                item.status.dueWithinSevenDays === true)
                        )
                    },
                )
                const groupedDues = filteredDueWithStatus.reduce(
                    (acc: any, due: any) => {
                        const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`
                        if (!acc[key]) {
                            acc[key] = {
                                id: due.id,
                                vesselID: due.vesselID,
                                vessel: due.vessel,
                                trainingTypeID: due.trainingTypeID,
                                trainingType: due.trainingType,
                                dueDate: due.dueDate,
                                status: due.status,
                                members: [],
                            }
                        }
                        acc[key].members.push(due.member)
                        return acc
                    },
                    {},
                )

                const mergedDues = Object.values(groupedDues).map(
                    (group: any) => {
                        const mergedMembers = group.members.reduce(
                            (acc: any, member: any) => {
                                const existingMember = acc.find(
                                    (m: any) => m.id === member.id,
                                )
                                if (existingMember) {
                                    existingMember.firstName = member.firstName
                                    existingMember.surname = member.surname
                                } else {
                                    acc.push(member)
                                }
                                return acc
                            },
                            [],
                        )
                        return {
                            id: group.id,
                            vesselID: group.vesselID,
                            vessel: group.vessel,
                            trainingTypeID: group.trainingTypeID,
                            trainingType: group.trainingType,
                            status: group.status,
                            dueDate: group.dueDate,
                            members: mergedMembers,
                        }
                    },
                )
                setTrainingSessionDues(mergedDues)
            }
        },
        onError: (error: any) => {
            console.error('readTrainingSessionDues error', error)
        },
    })
    const loadTrainingSessionDues = async (filter: any) => {
        const dueFilter: any = {}
        if (memberId > 0) {
            dueFilter.memberID = { eq: +memberId }
        }
        if (vesselId > 0) {
            dueFilter.vesselID = { eq: +vesselId }
        }
        if (filter.vesselID) {
            dueFilter.vesselID = filter.vesselID
        }
        if (filter.trainingTypes) {
            dueFilter.trainingTypeID = { eq: filter.trainingTypes.id.contains }
        }
        if (filter.members) {
            dueFilter.memberID = { eq: filter.members.id.contains }
        }
        if (filter.date) {
            dueFilter.dueDate = filter.date
        } else {
            dueFilter.dueDate = { ne: null }
        }
        await readTrainingSessionDues({
            variables: {
                filter: dueFilter,
            },
        })
    }

    useEffect(() => {
        if (isLoading) {
            const f: { members?: any } = { ...filter }
            if (+memberId > 0) {
                f.members = { id: { contains: +memberId } }
            }
            setFilter(f)
            loadTrainingSessionDues(f)
            loadTrainingList(0, f)
            setIsLoading(false)
        }
    }, [isLoading])

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="OopsYou do not have the permission to view this section." />
        )
    }

    return (
        <div className="w-full">
            <Filter
                onChange={handleFilterOnChange}
                vesselIdOptions={vesselIdOptions}
                trainingTypeIdOptions={trainingTypeIdOptions}
                trainerIdOptions={trainerIdOptions}
                memberIdOptions={crewIdOptions}
            />
            {queryTrainingListLoading || readTrainingSessionDuesLoading ? (
                <TableSkeleton />
            ) : (
                <>
                    <TrainingList trainingSessionDues={trainingSessionDues} />
                    <CustomPagination
                        page={page}
                        limit={limit}
                        visiblePageCount={5}
                        {...pageInfo}
                        onClick={(newPage: number) =>
                            handleNavigationClick(newPage)
                        }
                    />
                </>
            )}
        </div>
    )
}

export default CrewTrainingUpcomingOverdue

// Define training session due data type
interface TrainingSessionDue {
    id: number
    dueDate: string
    vesselID: number
    trainingTypeID: number
    vessel: {
        id: number
        title: string
    }
    trainingType: {
        id: number
        title: string
    }
    members: Array<{
        id: number
        firstName?: string
        surname?: string
    }>
    status: {
        class: string
        label: string
        isOverdue: boolean
        dueWithinSevenDays: boolean
    }
}

// Create column definitions for the training table
const createTrainingColumns = (isVesselView: boolean = false) =>
    createColumns<TrainingSessionDue>([
        {
            accessorKey: 'dueDate',
            header: 'Overdue / Upcoming',
            cellAlignment: 'left',
            cell: ({ row }) => {
                const due = row.original
                return (
                    <Link
                        href={`/crew-training/info?id=${due.id}`}
                        className="font-medium hover:underline">
                        {formatDate(due.dueDate)}
                    </Link>
                )
            },
        },
        {
            accessorKey: 'trainingType',
            header: 'Training/drill',
            cellAlignment: 'left',
            breakpoint: 'tablet-md',
            cell: ({ row }) => {
                const due = row.original
                return (
                    <div className="space-y-2">
                        <div>
                            {due.trainingType.title}
                            {!isVesselView && (
                                <span className="ml-1 inline-block tablet-md:hidden">
                                    : {due.vessel.title}
                                </span>
                            )}
                        </div>
                        {/* Mobile view: Show crew members and status */}
                        <div className="flex flex-col gap-2 tablet-md:hidden">
                            <CrewMembersList
                                members={due.members}
                                isVesselView={isVesselView}
                            />
                            <StatusBadge status={due.status} />
                        </div>
                    </div>
                )
            },
        },
        ...(isVesselView
            ? []
            : [
                  {
                      accessorKey: 'vessel',
                      header: 'Where',
                      cellAlignment: 'left' as const,
                      breakpoint: 'tablet-md' as const,
                      cell: ({ row }: { row: any }) => {
                          const due = row.original
                          return due.vessel.title
                      },
                  },
              ]),
        {
            accessorKey: 'members',
            header: 'Who',
            cellAlignment: 'left',
            breakpoint: 'landscape',
            cell: ({ row }) => {
                const due = row.original
                return (
                    <CrewMembersList
                        members={due.members}
                        isVesselView={isVesselView}
                    />
                )
            },
        },
        {
            accessorKey: 'status',
            header: '',
            cellAlignment: 'right',
            breakpoint: 'tablet-md',
            cell: ({ row }) => {
                const due = row.original
                return <StatusBadge status={due.status} />
            },
        },
    ])

// Component to render crew members list with popover for overflow
const CrewMembersList = ({
    members,
    isVesselView = false,
}: {
    members: TrainingSessionDue['members']
    isVesselView?: boolean
}) => {
    const displayMembers = members.slice(0, 3)
    const remainingMembers = members.slice(3)

    return (
        <div className="flex items-center flex-wrap gap-2">
            {displayMembers.map((member) => (
                <div
                    key={member.id}
                    className="inline-block border rounded-md px-2 py-1 text-nowrap bg-muted/50 text-sm">
                    {isVesselView
                        ? member.firstName || ''
                        : `${member.firstName || ''} ${member.surname || ''}`.trim()}
                </div>
            ))}
            {remainingMembers.length > 0 && (
                <Popover>
                    <PopoverTrigger asChild>
                        <Button variant="secondary" size="sm">
                            +{remainingMembers.length} more
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="p-2 w-64 max-h-64 overflow-auto">
                        <div className="space-y-2">
                            {remainingMembers.map((member) => (
                                <div key={member.id} className="py-1">
                                    {isVesselView
                                        ? member.firstName || ''
                                        : `${member.firstName || ''} ${member.surname || ''}`.trim()}
                                </div>
                            ))}
                        </div>
                    </PopoverContent>
                </Popover>
            )}
        </div>
    )
}

// Component to render status badge
const StatusBadge = ({ status }: { status: TrainingSessionDue['status'] }) => {
    return (
        <div
            className={`${status.class} px-3 py-1.5 text-nowrap inline-flex items-center rounded-md text-xs font-medium`}>
            {status.label}
        </div>
    )
}

// Row status evaluator for highlighting overdue/upcoming rows
const getRowStatus = (rowData: TrainingSessionDue): RowStatus => {
    if (rowData.status.isOverdue) {
        return 'overdue'
    }
    if (rowData.status.dueWithinSevenDays) {
        return 'upcoming'
    }
    return 'normal'
}

export const TrainingList = ({
    trainingSessionDues,
    isVesselView = false,
}: {
    trainingSessionDues: TrainingSessionDue[]
    isVesselView?: boolean
}) => {
    if (!trainingSessionDues?.length) {
        return null
    }

    const columns = createTrainingColumns(isVesselView)

    return (
        <FilteredTable
            columns={columns}
            data={trainingSessionDues}
            showToolbar={false}
            rowStatus={getRowStatus}
            pageSize={trainingSessionDues.length} // Show all rows without pagination
        />
    )
}
