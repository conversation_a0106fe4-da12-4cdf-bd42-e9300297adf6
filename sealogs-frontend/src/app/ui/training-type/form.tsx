'use client'

import { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import { InputSkeleton } from '../../../components/skeletons'
import { isEmpty, trim } from 'lodash'
import { useRouter } from 'next/navigation'
import {
    CREATE_TRAINING_TYPE,
    UPDATE_TRAINING_TYPE,
    DELETE_TRAINING_TYPE,
    CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
    CREATE_CUSTOMISED_COMPONENT_FIELD,
    UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
    UPDATE_CUSTOMISED_COMPONENT_FIELD,
    DELETE_CUSTOMISED_COMPONENT_FIELD,
} from '@/app/lib/graphQL/mutation'
import Editor from '../editor'
import { getVesselList, getTrainingTypeByID } from '@/app/lib/actions'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { H1, H2, H4 } from '@/components/ui/typography'
import { Input } from '@/components/ui/input'
import { Combobox } from '@/components/ui/comboBox'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Label } from '@/components/ui/label'
import { AlertDialogBody } from '@/components/ui'
import { Plus, Check, Trash, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { FooterWrapper } from '@/components/footer-wrapper'
// import SlidingPanel from 'react-sliding-side-panel'
// import { XMarkIcon } from '@heroicons/react/24/outline'
// import 'react-sliding-side-panel/lib/index.css'
import { TRAINING_TYPE_BY_ID } from '@/app/lib/graphQL/query'
import SeaLogsButton from '@/components/ui/sea-logs-button'
// import { AlertDialog, SeaLogsButton } from '@/app/components/Components'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '../../../../utils/cn'

const TrainingTypeForm = ({
    trainingTypeId = 0,
}: {
    trainingTypeId: number
}) => {
    const router = useRouter()
    const [trainingType, setTrainingType] = useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [hasFormErrors, setHasFormErrors] = useState(false)
    const [formErrors, setFormErrors] = useState({
        Title: '',
    })
    const [selectedTrainingType, setSelectedTrainingType] = useState<any>(null)
    const [trainingTypes, setTrainingTypes] = useState<any>()
    const [procedure, setProcedure] = useState(false)
    const [fieldProcedure, setFieldProcedure] = useState<any>(null)
    const [procedureList, setProcedureList] = useState<any>(null)
    const [displayAddNewProcedure, setDisplayAddNewProcedure] = useState(false)
    const [selectedProcedureField, setSelectedProcedureField] = useState<any>(
        [],
    )
    const [procedureToDelete, setProcedureToDelete] = useState<any>()
    const [displayDeleteDialog, setDisplayDeleteDialog] = useState(false)

    const handleSetTrainingType = (trainingType: any) => {
        setTrainingType(trainingType)
        setProcedure(trainingType?.procedure)
        setProcedureList(trainingType.customisedComponentField.nodes)
    }

    getTrainingTypeByID(trainingTypeId, handleSetTrainingType)

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        setVessels(activeVessels)
    }

    getVesselList(handleSetVessels)

    const handleInputChange = (e: any) => {
        const { name, value } = e.target
        const intValues = ['HighWarnWithin', 'MediumWarnWithin', 'OccursEvery']
        let newValue = value
        if (intValues.includes(name)) {
            newValue = +value
        }
        setTrainingType({
            ...trainingType,
            [name]: newValue,
            ID: +trainingTypeId,
        })
    }
    const handleVesselChange = (event: any) => {
        setTrainingType({
            ...trainingType,
            Vessels: event.map((v: any) => ({
                ID: v.value,
                Title: v.label,
            })),
        })
    }
    const [
        mutationUpdateTrainingType,
        { loading: mutationUpdateTrainingTypeLoading },
    ] = useMutation(UPDATE_TRAINING_TYPE, {
        onCompleted: (response: any) => {
            const data = response.updateTrainingType
            if (data.id > 0) {
                router.back()
                if (trainingTypeId === 0) {
                    router.push('/training-type')
                } else {
                    router.push(`/training-type/info?id=${trainingTypeId}`)
                }
            }
        },
        onError: (error: any) => {
            console.error('mutationUpdateTrainingType error', error)
        },
    })
    const [
        mutationCreateTrainingType,
        { loading: mutationCreateTrainingTypeLoading },
    ] = useMutation(CREATE_TRAINING_TYPE, {
        onCompleted: (response: any) => {
            const data = response.createTrainingType
            if (data.id > 0) {
                router.back()
                if (trainingTypeId === 0) {
                    router.push('/training-type')
                } else {
                    router.push(`/training-type/info?id=${trainingTypeId}`)
                }
            }
        },
        onError: (error: any) => {
            console.error('mutationCreateTrainingType error', error)
        },
    })
    const handleSave = async () => {
        let hasErrors = false
        let errors = {
            Title: '',
        }
        setFormErrors(errors)
        if (
            isEmpty(
                trim(
                    (
                        document.getElementById(
                            'nature-of-training',
                        ) as HTMLInputElement
                    ).value,
                ),
            )
        ) {
            hasErrors = true
            errors.Title = 'Nature of training is required'
        }
        if (hasErrors) {
            setHasFormErrors(true)
            setFormErrors(errors)
            return
        }

        const variables = {
            input: {
                id: trainingTypeId,
                title: (
                    document.getElementById(
                        'nature-of-training',
                    ) as HTMLInputElement
                ).value,
                occursEvery: trainingType.OccursEvery,
                highWarnWithin: trainingType.HighWarnWithin,
                mediumWarnWithin: trainingType.MediumWarnWithin,
                procedure: procedure ? procedure : trainingType.procedure,
                vessels: trainingType.Vessels?.map((v: any) => v.ID).join(','),
            },
        }
        if (trainingTypeId === 0) {
            await mutationCreateTrainingType({
                variables,
            })
        } else {
            await mutationUpdateTrainingType({
                variables,
            })
        }
    }
    const handleEditorChange = (content: any) => {
        setProcedure(content)
    }

    const [mutationDeleteTrainingType] = useMutation(DELETE_TRAINING_TYPE, {
        onCompleted: () => {
            router.push('/training-type')
        },
        onError: (error: any) => {
            console.error('mutationDeleteTrainingType error', error)
        },
    })

    const handleDeleteTrainingType = async () => {
        if (selectedTrainingType?.id) {
            await mutationDeleteTrainingType({
                variables: {
                    ids: [+selectedTrainingType.id],
                },
            })
            const tt = trainingTypes?.map((t: any) => {
                if (t.id === selectedTrainingType.id) {
                    return {
                        ...t,
                        Archived: !t.Archived,
                    }
                }
                return t
            })
            const filteredList = tt?.filter((t: any) => !t.archived)
            setTrainingTypes(filteredList)
        }
    }

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    const handleProcedureEditorChange = (content: any) => {
        setFieldProcedure(content)
        setSelectedProcedureField({
            ...selectedProcedureField,
            description: content,
        })
    }

    const handleSaveProcedureField = () => {
        const variables = {
            input: {
                fieldName: selectedProcedureField.title,
                sortOrder: +selectedProcedureField.sortOrder,
                status: 'Required',
                description: selectedProcedureField.description,
                trainingTypeID: +trainingTypeId,
            },
        }
        if (selectedProcedureField?.id > 0) {
            updateCustomisedLogBookComponent({
                variables: {
                    input: {
                        id: +selectedProcedureField.id,
                        fieldName: selectedProcedureField.fieldName,
                        sortOrder: +selectedProcedureField.sortOrder,
                        status: 'Required',
                        description:
                            selectedProcedureField.description == '<p><br></p>'
                                ? null
                                : selectedProcedureField.description,
                    },
                },
            })
            setSelectedProcedureField([])
            setDisplayAddNewProcedure(false)
            setFieldProcedure('')
        } else {
            createCustomisedLogBookComponent({
                variables,
            })
        }
    }

    const [createCustomisedLogBookComponent] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                const data = response.createCustomisedComponentField
                if (data.id > 0) {
                    refreshProcedureList()
                    setSelectedProcedureField([])
                    setDisplayAddNewProcedure(false)
                    setFieldProcedure('')
                }
            },
            onError: (error: any) => {
                console.error('createCustomisedLogBookComponent error', error)
            },
        },
    )

    const refreshProcedureList = () => {
        getProcedureList({
            variables: {
                id: +trainingTypeId,
            },
        })
    }

    const [getProcedureList] = useLazyQuery(TRAINING_TYPE_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data =
                response.readOneTrainingType.customisedComponentField.nodes
            if (data) {
                setProcedureList(data)
            }
        },
        onError: (error: any) => {
            console.error('getProcedureList error', error)
        },
    })

    const handleProcedureChange = (id: number, status: boolean) => {
        const updatedList = procedureList.map((procedure: any) => {
            if (procedure.id === id) {
                return {
                    ...procedure,
                    status: status ? 'Required' : 'Off',
                }
            }
            return procedure
        })
        updateCustomisedLogBookComponent({
            variables: {
                input: {
                    id: +id,
                    status: status ? 'Required' : 'Off',
                },
            },
        })
        setProcedureList(updatedList)
    }

    const [updateCustomisedLogBookComponent] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                const data = response.updateCustomisedComponentField
                if (data.id > 0) {
                    refreshProcedureList()
                }
            },
            onError: (error: any) => {
                console.error('updateCustomisedLogBookComponent error', error)
            },
        },
    )

    const handleDeleteProcedure = (id: number) => {
        setDisplayDeleteDialog(true)
        setProcedureToDelete(id)
    }

    const [deleteCustomisedLogBookComponent] = useMutation(
        DELETE_CUSTOMISED_COMPONENT_FIELD,
        {
            fetchPolicy: 'no-cache',
            onCompleted: (response: any) => {
                refreshProcedureList()
                setDisplayDeleteDialog(false)
                setProcedureToDelete(0)
            },
            onError: (error: any) => {},
        },
    )

    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <>
            <H1 className="mb-4 phablet:mb-6 text-xl phablet:text-2xl landscape:text-3xl">
                {trainingTypeId === 0 ? 'New' : 'Edit'} Training Type
            </H1>

            {/* Basic Information Card */}
            <Card className="mb-4 phablet:mb-6">
                <div className="grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4">
                    <div className="space-y-2">
                        <H4>Training type details</H4>
                    </div>
                    <div className="landscape:col-span-2 space-y-4">
                        <Label
                            label="Nature of training"
                            htmlFor="nature-of-training"
                            required>
                            {!trainingType && trainingTypeId > 0 ? (
                                <InputSkeleton />
                            ) : (
                                <Input
                                    id="nature-of-training"
                                    placeholder="Nature of training"
                                    name="Title"
                                    defaultValue={trainingType?.title || ''}
                                    onChange={handleInputChange}
                                    type="text"
                                    required
                                />
                            )}
                            {hasFormErrors && formErrors.Title && (
                                <p className="text-sm text-destructive">
                                    {formErrors.Title}
                                </p>
                            )}
                        </Label>

                        <Label label="Assigned vessels" htmlFor="vessels">
                            {vessels ? (
                                <Combobox
                                    id="vessels"
                                    options={vessels?.map((vessel: any) => ({
                                        value: vessel.id.toString(),
                                        label: vessel.title,
                                        vessel: {
                                            id: vessel.id,
                                            title: vessel.title,
                                            icon: vessel.icon,
                                            iconMode: vessel.iconMode,
                                            photoID: vessel.photoID,
                                        },
                                    }))}
                                    defaultValues={trainingType?.vessels?.nodes?.map(
                                        (v: any) => {
                                            // Find the full vessel data from the vessels list to get icon info
                                            const fullVesselData =
                                                vessels?.find(
                                                    (vessel: any) =>
                                                        vessel.id === v.id,
                                                )
                                            return {
                                                value: v.id.toString(),
                                                label: v.title,
                                                vessel: fullVesselData
                                                    ? {
                                                          id: fullVesselData.id,
                                                          title: fullVesselData.title,
                                                          icon: fullVesselData.icon,
                                                          iconMode:
                                                              fullVesselData.iconMode,
                                                          photoID:
                                                              fullVesselData.photoID,
                                                      }
                                                    : {
                                                          id: v.id,
                                                          title: v.title,
                                                          icon: null,
                                                          iconMode: 'Icon',
                                                          photoID: '0',
                                                      },
                                            }
                                        },
                                    )}
                                    onChange={handleVesselChange}
                                    placeholder="Select vessels"
                                    multi
                                />
                            ) : (
                                <InputSkeleton />
                            )}
                        </Label>
                    </div>
                </div>
            </Card>

            {/* Frequency Settings Card */}
            <Card className="mb-4 phablet:mb-6">
                <div className="grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4">
                    <div className="space-y-2">
                        <H4>Frequency</H4>
                    </div>
                    <div className="landscape:col-span-2 space-y-4">
                        <div className="grid grid-cols-1 phablet:grid-cols-2 landscape:grid-cols-1 gap-4">
                            <Label
                                label="Occurs Every (days)"
                                htmlFor="OccursEvery">
                                {!trainingType && trainingTypeId > 0 ? (
                                    <InputSkeleton />
                                ) : (
                                    <Input
                                        id="OccursEvery"
                                        name="OccursEvery"
                                        defaultValue={
                                            trainingType?.occursEvery || 0
                                        }
                                        onChange={handleInputChange}
                                        type="number"
                                        step={1}
                                        min={0}
                                    />
                                )}
                            </Label>

                            <Label
                                label="Medium Warning Within (e.g. 5 days)"
                                htmlFor="MediumWarnWithin">
                                {!trainingType && trainingTypeId > 0 ? (
                                    <InputSkeleton />
                                ) : (
                                    <Input
                                        id="MediumWarnWithin"
                                        name="MediumWarnWithin"
                                        defaultValue={
                                            trainingType?.mediumWarnWithin || 0
                                        }
                                        onChange={handleInputChange}
                                        type="number"
                                        step={1}
                                        min={0}
                                    />
                                )}
                            </Label>
                        </div>

                        <Label
                            label="High Warning Within (e.g. 1 day)"
                            htmlFor="HighWarnWithin">
                            {!trainingType && trainingTypeId > 0 ? (
                                <InputSkeleton />
                            ) : (
                                <Input
                                    id="HighWarnWithin"
                                    name="HighWarnWithin"
                                    defaultValue={
                                        trainingType?.highWarnWithin || 0
                                    }
                                    onChange={handleInputChange}
                                    type="number"
                                    step={1}
                                    min={0}
                                />
                            )}
                        </Label>
                    </div>
                </div>
            </Card>
            <Card className="mb-4 phablet:mb-6">
                <div className="grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4">
                    <div className="space-y-2">
                        <H4>Procedure added</H4>
                    </div>
                    <div className="landscape:col-span-2 space-y-4">
                        {procedureList?.length > 0 ? (
                            <div className="flex flex-col gap-2 pb-3">
                                {procedureList?.map(
                                    (procedure: any, index: number) => (
                                        <div
                                            key={procedure.id}
                                            className="flex justify-between items-center bg-sllightblue-50 border-b border-border">
                                            <div className="flex items-center gap-2">
                                                <RadioGroup
                                                    variant="horizontal"
                                                    gap={'none'}
                                                    value={
                                                        procedure.status ===
                                                        'Required'
                                                            ? 'yes'
                                                            : 'no'
                                                    }
                                                    onValueChange={(value) => {
                                                        handleProcedureChange(
                                                            procedure.id,
                                                            value === 'yes'
                                                                ? true
                                                                : false,
                                                        )
                                                    }}>
                                                    <div
                                                        className={cn(
                                                            'flex w-[48px] bg-cinnabar-100 justify-center py-3 standard:p-0 standard:items-center',
                                                        )}>
                                                        <RadioGroupItem
                                                            value="no"
                                                            id={`${index}-no_radio`}
                                                            variant="destructive"
                                                            size="lg"
                                                        />
                                                    </div>

                                                    <div
                                                        className={cn(
                                                            'flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center',
                                                        )}>
                                                        <RadioGroupItem
                                                            value="yes"
                                                            id={`${index}-yes_radio`}
                                                            variant="success"
                                                            size="lg"
                                                        />
                                                    </div>
                                                </RadioGroup>
                                                <div
                                                    className="text-sldarkblue-800 font-semibold cursor-pointer"
                                                    onClick={() => {
                                                        setSelectedProcedureField(
                                                            {
                                                                ...procedure,
                                                                description:
                                                                    procedure.description,
                                                            },
                                                        )
                                                        setDisplayAddNewProcedure(
                                                            true,
                                                        )
                                                        setFieldProcedure(
                                                            procedure.description,
                                                        )
                                                    }}>
                                                    {procedure.fieldName}
                                                </div>
                                            </div>
                                            <Button
                                                variant={'text'}
                                                size={'icon'}
                                                onClick={() => {
                                                    handleDeleteProcedure(
                                                        procedure.id,
                                                    )
                                                }}>
                                                <Trash2 />
                                            </Button>
                                        </div>
                                    ),
                                )}
                            </div>
                        ) : (
                            <p>No procedures added</p>
                        )}
                        <SeaLogsButton
                            action={() => {
                                setDisplayAddNewProcedure(true)
                            }}
                            text="Add new check"
                            type="text"
                            color="sky"
                            icon="plus"
                        />
                    </div>
                </div>
            </Card>
            {/* Procedure Card */}
            <Card className="mb-4 phablet:mb-6">
                <div className="grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4">
                    <div className="space-y-2">
                        <H4>Procedure</H4>
                    </div>
                    <div className="landscape:col-span-2">
                        {!trainingType && trainingTypeId > 0 ? (
                            <InputSkeleton />
                        ) : (
                            <Editor
                                name="Procedure"
                                content={procedure || ''}
                                handleEditorChange={handleEditorChange}
                            />
                        )}
                    </div>
                </div>
            </Card>
            <FooterWrapper>
                <div className="flex gap-2">
                    <Button variant="back" onClick={() => router.back()}>
                        Cancel
                    </Button>
                    {trainingTypeId > 0 && (
                        <Button
                            variant="destructive"
                            onClick={() =>
                                setSelectedTrainingType(trainingType)
                            }>
                            Delete
                        </Button>
                    )}
                </div>
                <Button
                    onClick={handleSave}
                    isLoading={
                        mutationUpdateTrainingTypeLoading ||
                        mutationCreateTrainingTypeLoading
                    }
                    iconLeft={trainingTypeId === 0 ? Plus : Check}>
                    {trainingTypeId === 0 ? 'Create' : 'Update'}
                </Button>
            </FooterWrapper>

            <AlertDialogNew
                openDialog={!!selectedTrainingType}
                setOpenDialog={() => setSelectedTrainingType(null)}
                handleCreate={handleDeleteTrainingType}
                actionText="Delete"
                variant="danger"
                title="Delete Training Type"
                description={`Are you sure you want to delete ${trainingType?.title || 'this training type'}?`}
            />
            <AlertDialogNew
                openDialog={displayAddNewProcedure}
                setOpenDialog={setDisplayAddNewProcedure}
                handleCreate={handleSaveProcedureField}
                size="xl"
                actionText="Create Procedure"
                title="Create new Procedure">
                <AlertDialogBody className="space-y-4">
                    {displayAddNewProcedure && (
                        <div slot="content" className="mb-4">
                            <Input
                                id={`field-name`}
                                name={`field-name`}
                                type="text"
                                className={'w-full mb-4'}
                                placeholder="Field Name"
                                value={selectedProcedureField.fieldName}
                                onChange={(e) => {
                                    setSelectedProcedureField({
                                        ...selectedProcedureField,
                                        title: e.target.value,
                                    })
                                }}
                            />
                            <Input
                                id={`field-sort-order`}
                                name={`field-sort-order`}
                                type="number"
                                className={'w-full mb-4'}
                                min="0"
                                placeholder="Sort order"
                                value={selectedProcedureField.sortOrder}
                                onChange={(e) => {
                                    setSelectedProcedureField({
                                        ...selectedProcedureField,
                                        sortOrder: e.target.value,
                                    })
                                }}
                            />
                            <Label
                                htmlFor="field-description"
                                label=" Procedure description">
                                <Editor
                                    id="field-description"
                                    placeholder="Description (Optional)"
                                    content={fieldProcedure}
                                    handleEditorChange={
                                        handleProcedureEditorChange
                                    }
                                />
                            </Label>
                        </div>
                    )}
                </AlertDialogBody>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={displayDeleteDialog}
                setOpenDialog={setDisplayDeleteDialog}
                handleCreate={() => {
                    deleteCustomisedLogBookComponent({
                        variables: {
                            ids: [+procedureToDelete],
                        },
                    })
                }}
                size="xl"
                actionText="Delete"
                title="Delete Procedure!">
                <AlertDialogBody className="space-y-4"></AlertDialogBody>
            </AlertDialogNew>
        </>
    )
}
export default TrainingTypeForm
