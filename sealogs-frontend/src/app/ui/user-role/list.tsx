'use client'
import { useEffect, useState } from 'react'
import { Heading } from 'react-aria-components'
import { List, TableSkeleton } from '@/components/skeletons'
import Link from 'next/link'
import { getSeaLogsGroups } from '@/app/lib/actions'
import { preventCrewAccess } from '@/app/helpers/userHelper'
import { useRouter } from 'next/navigation'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'

const UserRoleList = () => {
    const router = useRouter()
    const [userRoles, setUserRoles] = useState([])
    getSeaLogsGroups(setUserRoles)

    useEffect(() => {
        preventCrewAccess()
    }, [])
    return (
        <div className="w-full p-0">
            <div className="flex justify-between items-center">
                <Heading className="  text-3xl  ">User Roles</Heading>
                <SeaLogsButton
                    // link={`/settings/user-role/create`}
                    action={() => {
                        router.push(`/settings/user-role/create`)
                    }}
                    text="New User Role"
                    color="sky"
                    type="primary"
                    icon="check"
                />
            </div>
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {!userRoles ? (
                        <TableSkeleton />
                    ) : (
                        <TableWrapper
                            headings={[
                                'Role:firstHead',
                                'Code',
                                'Description',
                            ]}>
                            {userRoles.map((userRole: any) => (
                                <tr
                                    key={userRole.id}
                                    className={`group border-b  hover:   `}>
                                    <td className="pl-2 py-3 lg:px-6">
                                        <Link
                                            href={`/settings/user-role/edit?id=${userRole.id}`}
                                            className="">
                                            {userRole.title}
                                        </Link>
                                    </td>
                                    <td className="px-2">{userRole.code}</td>
                                    <td className="px-2">
                                        {userRole.description}
                                    </td>
                                </tr>
                            ))}
                        </TableWrapper>
                    )}
                </div>
            </div>
        </div>
    )
}

export default UserRoleList
