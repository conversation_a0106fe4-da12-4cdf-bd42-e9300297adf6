'use client'

import Filter from '@/components/filter'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import { List, TableSkeleton } from '../../../components/skeletons'
import Link from 'next/link'
import { GET_KEY_CONTACTS } from '@/app/lib/graphQL/query'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { H3 } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Check, Users } from 'lucide-react'
import TableWrapper from '@/components/ui/table-wrapper'
import { Card, CardContent, CardHeader } from '@/components/ui'

export interface IKeyContactPermission {
    EDIT_KEY_CONTACT?: boolean
    DELETE_KEY_CONTACT?: boolean
}

export default function KeyContactList() {
    const [keyContacts, setKeyContacts] = useState([] as any)
    const [groupByCompany, setGroupByCompany] = useState(false)
    const [permission, setPermission] = useState<IKeyContactPermission>({
        EDIT_KEY_CONTACT: undefined,
        DELETE_KEY_CONTACT: undefined,
    })

    const handleFilterChange = ({
        type,
        data,
    }: {
        type: string
        data: any
    }) => {
        let searchKeywordFilters: any[] = []
        if (type === 'keyword' && data.value !== '') {
            const fieldsToFilter = ['firstName', 'surname', 'phone', 'email']

            searchKeywordFilters = fieldsToFilter.map(function (field) {
                return {
                    [field]: { contains: data.value },
                }
            })
        }

        loadData(searchKeywordFilters)
    }

    const [queryGetKeyContacts, { called, loading }] = useLazyQuery(
        GET_KEY_CONTACTS,
        {
            fetchPolicy: 'cache-and-network',
            // onCompleted: (response: any) => {
            //     const data = response.readSuppliers.nodes
            //     if (data) {
            //         setSuppliers(data)
            //     }
            // },
            onError: (error: any) => {
                console.error('querySupplier error', error)
            },
        },
    )

    const loadData = async (searchKeywordFilters: any[] = []) => {
        if (searchKeywordFilters.length > 0) {
            const promises = searchKeywordFilters.map(
                async (keywordFilter: any) => {
                    return await queryGetKeyContacts({
                        variables: {
                            filter: keywordFilter,
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) => r.data.readKeyContacts.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readKeyContacts.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            setKeyContacts(responses)
        } else {
            const { data } = await queryGetKeyContacts()
            setKeyContacts(data?.readKeyContacts.nodes ?? [])
        }
    }

    const initPermission = () => {
        const permissions = getPermissions('EDIT_KEY_CONTACT')

        Object.keys(permission).forEach((value) => {
            const hasThisPermission = hasPermission(value, permissions)

            setPermission((prev) => ({ ...prev, [value]: hasThisPermission }))
        })
    }

    useEffect(() => {
        loadData()
        initPermission()
    }, [])

    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <H3 className="font-light">Key Contacts</H3>
                <div className="flex items-center gap-2">
                    <Button
                        variant={groupByCompany ? 'primary' : 'secondary'}
                        onClick={() => setGroupByCompany((prev) => !prev)}
                        iconLeft={Users}>
                        Group by Company
                    </Button>
                    {permission.EDIT_KEY_CONTACT && (
                        <Link href="/key-contacts/create">
                            <Button iconLeft={Check}>New Key Contact</Button>
                        </Link>
                    )}
                </div>
            </div>
            <Card>
                <CardHeader>
                    <Filter onChange={handleFilterChange} />
                </CardHeader>
                <CardContent>
                    <div className="flex w-full justify-start flex-col md:flex-row items-start">
                        {called && loading ? (
                            <TableSkeleton />
                        ) : (
                            <>
                                {keyContacts.length == 0 ? (
                                    <div className="text-center font-semibold w-full flex items-center justify-center h-20">
                                        No Data Found
                                    </div>
                                ) : (
                                    <DataPresentation
                                        data={keyContacts}
                                        groupByCompany={groupByCompany}
                                    />
                                )}
                            </>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

const DataPresentation = ({
    data,
    groupByCompany,
}: {
    data: any
    groupByCompany: boolean
}) => {
    if (!groupByCompany) {
        return (
            <TableWrapper
                headings={[
                    'Key Contacts:firstHead',
                    'Email',
                    'Phone',
                    'Address',
                    'Company',
                ]}>
                {(data as any[]).map((keyContact: any, index: number) => (
                    <tr key={index} className={`border-b  hover:bg-white `}>
                        <td className="px-2 lg:px-6 py-3 whitespace-nowrap text-left">
                            <div className="text-sm font-medium text-gray-900 ">
                                <Link
                                    href={`/key-contacts/edit?id=${keyContact.id}`}>
                                    {keyContact.firstName} {keyContact.surname}
                                </Link>
                            </div>
                        </td>
                        <td className="px-2 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900 ">
                                {keyContact.email ?? '-'}
                            </div>
                        </td>
                        <td className="px-2 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900 ">
                                {keyContact.phone ?? '-'}
                            </div>
                            <div className="text-sm text-gray-900 ">
                                {keyContact.cellPhone ?? '-'}
                            </div>
                        </td>
                        <td className="px-2 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900 ">
                                {keyContact.address ?? '-'}
                            </div>
                        </td>
                        <td className="px-2 py-3 whitespace-nowrap">
                            <div className="text-sm text-gray-900 ">
                                {keyContact.company?.title ?? '-'}
                            </div>
                        </td>
                    </tr>
                ))}
            </TableWrapper>
        )
    }

    const groupedData = data.reduce((prev: any, current: any) => {
        const companyID = `${current.companyID ?? 0}`

        if (Object.hasOwn(prev, companyID)) {
            return prev
        }

        const companyKeyContacts = data.filter(
            (item: any) => item.companyID === current.companyID,
        )

        return {
            ...prev,
            [companyID]: companyKeyContacts,
        }
    }, {})

    return (
        <div className="w-full">
            <div className="flex flex-col gap-4">
                {Object.entries(groupedData).map(function (grouped) {
                    const [, keyContacts] = grouped as any

                    const companyName = keyContacts[0].company.title
                    return (
                        <div>
                            <div className="bg-white border font-bold border-gray-100 pl-5 max-w-sm rounded-t-lg py-2 px-3">
                                {companyName}
                            </div>
                            <TableWrapper
                                headings={[
                                    'Key Contacts:firstHead',
                                    'Email',
                                    'Phone',
                                    'Address',
                                ]}>
                                {(keyContacts as any[]).map(
                                    (keyContact: any, index: number) => (
                                        <tr
                                            key={index}
                                            className={`border-b  hover:bg-white `}>
                                            <td className="px-2 lg:px-6 py-3 whitespace-nowrap text-left w-[30%]">
                                                <div className="text-sm font-medium text-gray-900 ">
                                                    <Link
                                                        href={`/key-contacts/edit?id=${keyContact.id}`}>
                                                        {keyContact.firstName}{' '}
                                                        {keyContact.surname}
                                                    </Link>
                                                </div>
                                            </td>
                                            <td className="px-2 py-3 whitespace-nowrap w-[20%]">
                                                <div className="text-sm text-gray-900 ">
                                                    {keyContact.email ?? '-'}
                                                </div>
                                            </td>
                                            <td className="px-2 py-3 whitespace-nowrap w-[20%]">
                                                <div className="text-sm text-gray-900 ">
                                                    {keyContact.phone ?? '-'}
                                                </div>
                                                <div className="text-sm text-gray-900 ">
                                                    {keyContact.cellPhone ??
                                                        '-'}
                                                </div>
                                            </td>
                                            <td className="px-2 py-3 whitespace-nowrap w-[30%]">
                                                <div className="text-sm text-gray-900 ">
                                                    {keyContact.address ?? '-'}
                                                </div>
                                            </td>
                                        </tr>
                                    ),
                                )}
                            </TableWrapper>
                        </div>
                    )
                })}
            </div>
        </div>
    )
}
