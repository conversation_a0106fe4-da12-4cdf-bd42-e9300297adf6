'use client'

import { redirect } from 'next/navigation'
import { useState } from 'react'
import Link from 'next/link'
import { TrainingSessionInfoSkeleton } from '@/components/skeletons'
import Image from 'next/image'
import { getTrainingSessionByID } from '@/app/lib/actions'
import { formatDate } from '@/app/helpers/dateHelper'
import { stripHtmlTags } from '@/app/helpers/stringHelper'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { H4, P } from '@/components/ui/typography'
import { Pencil, Calendar } from 'lucide-react'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { Card } from '@/components/ui'

const CrewTrainingInfo = ({ trainingID }: { trainingID: number }) => {
    if (trainingID <= 0) {
        redirect('/crew-training')
    }

    const [training, setTraining] = useState<any>()
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    getTrainingSessionByID(trainingID, setTraining)

    if (!training) {
        return <TrainingSessionInfoSkeleton />
    }

    const getProcedures = () => {
        return training?.trainingTypes?.nodes
            ?.map((type: any) => {
                return type.customisedComponentField.nodes.length > 0
                    ? {
                          id: type.id,
                          title: type.title,
                          fields: [
                              ...type.customisedComponentField.nodes,
                          ]?.sort(
                              (a: any, b: any) => a.sortOrder - b.sortOrder,
                          ),
                      }
                    : null
            })
            .filter((type: any) => type !== null)
    }

    const getFieldStatus = (field: any) => {
        const fieldStatus = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldStatus?.status || ''
    }
    const getComment = (field: any) => {
        const fieldComment = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        return fieldComment?.comment || field.comment
    }

    const showCommentPopup = (field: any) => {
        const fieldComment = training?.procedureFields?.nodes?.find(
            (procedureField: any) =>
                procedureField.customisedComponentFieldID == field.id,
        )
        setCurrentComment(fieldComment?.comment || '')
        setOpenCommentAlert(true)
    }

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <Card className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <Badge className="text-green-700 w-fit bg-green-50 hover:bg-green-100">
                            <Calendar className="h-3 w-3 mr-1" />
                            {training?.date
                                ? formatDate(training.date, false)
                                : 'No date set'}
                        </Badge>
                    </div>
                    <h1 className="text-2xl md:text-3xl font-medium">
                        <span className="text-muted-foreground font-normal mr-2">
                            Training Session:
                        </span>
                        <span>{training?.vessel.title}</span>
                    </h1>
                </div>
                <Button
                    asChild
                    variant='warning'
                    className="w-fit shadow-sm">
                    <Link href={`/crew-training/edit?id=${training.id}`}>
                        Edit Session
                    </Link>
                </Button>
            </Card>

            {/* Training Details Section */}
            <div className="space-y-6">
                <Card>
                    <div>
                        <H4>Training Details</H4>
                        <P>
                            Information about the trainer and type of training
                            conducted.
                        </P>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <h5 className="font-medium text-foreground">
                                Trainer
                            </h5>
                            <p className="text-base">{`${training?.trainer?.firstName || ''} ${training?.trainer?.surname || ''}`}</p>
                        </div>

                        <div className="space-y-2">
                            <h5 className="font-medium text-foreground">
                                Nature of Training
                            </h5>
                            <p className="text-base">
                                {training?.trainingTypes?.nodes
                                    .map((t: any) => t.title)
                                    .join(', ')}
                            </p>
                        </div>
                    </div>
                </Card>

                {/* Members Section */}
                <Card className="space-y-4">
                    <div>
                        <H4>Members</H4>
                        <P>
                            Crew members who participated in this training
                            session.
                        </P>
                    </div>
                    <div className="flex flex-wrap gap-2">
                        {training?.members?.nodes.map(
                            (m: any, index: number) => (
                                <Badge
                                    variant="outline"
                                    key={index}
                                    className="text-sm w-fit py-1">
                                    {`${m.firstName || ''} ${m.surname || ''}`}
                                </Badge>
                            ),
                        )}
                    </div>
                </Card>

                {/* Summary Section */}
                <Card className="space-y-4">
                    <div>
                        <H4>Summary</H4>
                        <P>
                            Training procedures completed and overall session
                            summary.
                        </P>
                    </div>
                    <div className="bg-muted/20 p-4 rounded-md">
                        {getProcedures().length > 0 && (
                            <div className="space-y-4 mb-4">
                                {getProcedures().map((type: any) => (
                                    <div
                                        key={type.id}
                                        className="bg-sllightblue-100 border border-sllightblue-200 rounded-md p-4">
                                        <h3 className="text-lg font-medium leading-6 text-gray-9000 mb-4">
                                            {type.title}
                                        </h3>
                                        <CheckField>
                                            {type.fields.filter(
                                                (field: any) =>
                                                    field.status === 'Required',
                                            ).length > 0 && (
                                                <CheckFieldTopContent />
                                            )}
                                            <CheckFieldContent>
                                                {type.fields.map(
                                                    (field: any) => (
                                                        <DailyCheckField
                                                            locked={true}
                                                            key={field.id}
                                                            displayField={
                                                                field.status ===
                                                                'Required'
                                                            }
                                                            displayDescription={
                                                                field.description
                                                            }
                                                            displayLabel={
                                                                field.fieldName
                                                            }
                                                            inputId={field.id}
                                                            defaultNoChecked={
                                                                getFieldStatus(
                                                                    field,
                                                                ) === 'Not_Ok'
                                                            }
                                                            defaultYesChecked={
                                                                getFieldStatus(
                                                                    field,
                                                                ) === 'Ok'
                                                            }
                                                            commentAction={() =>
                                                                showCommentPopup(
                                                                    field,
                                                                )
                                                            }
                                                            comment={getComment(
                                                                field,
                                                            )}
                                                            handleNoChange={() => {}}
                                                            handleYesChange={() => {}}
                                                        />
                                                    ),
                                                )}
                                            </CheckFieldContent>
                                        </CheckField>
                                    </div>
                                ))}
                            </div>
                        )}
                        <div className="whitespace-pre-line">
                            {training.trainingSummary
                                ? stripHtmlTags(training.trainingSummary)
                                : 'No summary provided.'}
                        </div>
                    </div>
                </Card>

                {/* Signatures Section */}
                <Card className="space-y-6">
                    <div>
                        <H4>Signatures</H4>
                        <P>
                            Digital signatures from training participants
                            confirming completion.
                        </P>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {training.signatures?.nodes.map((s: any) => (
                            <div
                                key={s.memberID}
                                className="border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                <div className="p-4 bg-accent">
                                    <h5 className="text-base font-medium">
                                        {s.member.firstName} {s.member.surname}
                                    </h5>
                                </div>
                                <div className="bg-background border-t border-border p-4 h-[120px] flex items-center justify-center">
                                    {s.signatureData ? (
                                        <Image
                                            src={
                                                s.signatureData ||
                                                '/placeholder.svg'
                                            }
                                            alt={`Signature of ${s.member.firstName} ${s.member.surname}`}
                                            width={220}
                                            height={80}
                                            className="object-contain"
                                        />
                                    ) : (
                                        <span className="text-muted-foreground text-sm italic">
                                            No signature provided
                                        </span>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </Card>

                <Separator className="my-4" />

                {/* Footer Information */}
                <div className="bg-muted/20 py-4 px-6 rounded-lg text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                        <span>Training ID: {training.id}</span>
                        <Separator orientation="vertical" className="h-4" />
                        <span>
                            Last updated:{' '}
                            {formatDate(training.updatedAt || training.date)}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CrewTrainingInfo
