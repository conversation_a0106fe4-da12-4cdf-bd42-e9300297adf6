'use client'

import { useMediaQuery } from '@reactuses/core'
import { useSearchParams } from 'next/navigation'
import { Dispatch, useEffect, useMemo, useState } from 'react'
import EngineChecks from '../engine-checks'
import ComprehensiveEngineLogs from '../comprehensive-engine-logs'
import CrewSupernumerary from '../../crew/supernumerary'
import { isCrew } from '@/app/helpers/userHelper'
import Crew from '../../crew/crew'
import DailyChecks from '../../daily-checks/checks'
import LogBookWeather from '../weather'
import TripLog from '../trip-log'
import LogEntrySignOff from '../sign-off'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useQueryState } from 'nuqs'
import { ReadVessels } from './queries'
import { useLazyQuery } from '@apollo/client'
import VesselModel from '@/app/offline/models/vessel'

interface IProps {
    logentryID: number
    vessel: any
    client: any
    offline: boolean
    logbook: any
    logBookConfig: any
    loaded: boolean
    locked: boolean
    fuel: any
    updateFuel: (fuel: any) => void
    edit_logBookEntry: boolean
    logEntrySections: any
    supernumerary: any
    setSupernumerary: Dispatch<any>
    crewMembers: any
    setCrewMembers: Dispatch<any>
    crew: any
    crewWelfare: any
    updateCrewWelfare: (crewWelfare: any) => void
    crewMembersList: any
    vesselDailyCheck: any
    setVesselDailyCheck: any
    masterID: number
    tripReport: any
    updateTripReport: Function
    fuelLogs: any
    signOff: any
    updateSignOff: (signOff: any) => void
    prevComments: any
    setPrevComments: Dispatch<any>
    logBookStartDate: any
}

export default function LogEntryMainContent({
    logentryID,
    offline,
    vessel,
    logbook,
    client,
    logBookConfig,
    loaded,
    locked,
    fuel,
    updateFuel,
    edit_logBookEntry,
    logEntrySections,
    supernumerary,
    setSupernumerary,
    crewMembers,
    setCrewMembers,
    crew,
    crewWelfare,
    updateCrewWelfare,
    crewMembersList,
    vesselDailyCheck,
    setVesselDailyCheck,
    masterID,
    tripReport,
    updateTripReport,
    fuelLogs,
    signOff,
    updateSignOff,
    prevComments,
    setPrevComments,
    logBookStartDate,
}: IProps) {
    const isLargeScreen = useMediaQuery('(min-width: 1280px)')
    const searchParams = useSearchParams()

    const [imCrew, setImCrew] = useState(false)
    const [isLoading, setIsLoading] = useState(true)
    const [vessels, setVessels] = useState<any>(false)
    const [createdTab, setCreatedTab] = useState(false)
    const [currentTrip, setCurrentTrip] = useState<any>(false)

    const vesselModel = new VesselModel()
    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', { defaultValue: 'crew' })

    useEffect(() => {
        const firstTab = searchParams.get('firstTab') ?? 0
        const commentTab = firstTab + ''

        if (firstTab != 0 && commentTab != 'crew' && commentTab !== tab) {
            setTab(commentTab)
        }
    }, [searchParams, tab])

    const sortedCrew = useMemo(
        () => [
            ...(logbook?.vehicle?.seaLogsMembers?.nodes.filter(
                (vcrew: any) => !vcrew.archived,
            ) ?? []),
            ...(crew?.filter(
                (crew: any) =>
                    !logbook?.vehicle?.seaLogsMembers?.nodes
                        .filter((vcrew: any) => !vcrew.archived)
                        .map((vcrew: any) => vcrew.id)
                        .includes(crew.id),
            ) ?? []),
        ],
        [logbook, crew],
    )

    const displayWeatherField = useMemo(() => {
        const weather =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'Weather_LogBookComponent',
            )
        if (weather?.length > 0) {
            if (weather[0]?.active) {
                return true
            }
        }
        return false
    }, [logBookConfig])
    const [queryVessels] = useLazyQuery(ReadVessels, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (queryVesselResponse: any) => {
            if (queryVesselResponse.readVessels.nodes) {
                setVessels(queryVesselResponse.readVessels.nodes)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })
    const loadVessels = async (offline: boolean) => {
        if (offline) {
            const response = await vesselModel.getAll()
            setVessels(response)
        } else {
            await queryVessels()
        }
    }
    useEffect(() => {
        if (isLoading) {
            setImCrew(isCrew() || false)
            loadVessels(offline)
            setIsLoading(false)
        }
    }, [isLoading])
    // getVesselList(setVessels, offline)
    // Define tabs as a JSON object for easier management
    const tabItems = [
        {
            id: 'crew',
            label: 'Crew',
            component: crew && loaded && (
                <Crew
                    offline={offline}
                    crewSections={crewMembers}
                    allCrew={sortedCrew}
                    logBookEntryID={logentryID}
                    locked={locked}
                    logBookConfig={logBookConfig}
                    setCrewMembers={setCrewMembers}
                    crewWelfareCheck={crewWelfare}
                    updateCrewWelfare={updateCrewWelfare}
                    vessel={vessel}
                    masterID={masterID}
                    logEntrySections={logEntrySections}
                    crewMembersList={crewMembersList}
                />
            ),
        },
        {
            id: 'pre-departure-checks',
            label: 'Pre-Departure Checks',
            component: logBookConfig && (
                <DailyChecks
                    offline={offline}
                    vesselDailyCheck={vesselDailyCheck}
                    logBookConfig={logBookConfig}
                    setVesselDailyCheck={setVesselDailyCheck}
                    locked={locked}
                    edit_logBookEntry={edit_logBookEntry}
                />
            ),
        },
        {
            id: 'weather',
            label: 'Weather',
            component: displayWeatherField && (
                <LogBookWeather
                    offline={offline}
                    logBookConfig={logBookConfig}
                    logbook={logbook}
                />
            ),
        },
        {
            id: 'trip-log',
            label: 'Trip Log',
            component: logBookConfig && (
                <TripLog
                    offline={offline}
                    tripReport={tripReport}
                    logBookConfig={logBookConfig}
                    updateTripReport={updateTripReport}
                    locked={locked || !edit_logBookEntry}
                    crewMembers={crewMembers}
                    masterID={masterID}
                    createdTab={createdTab}
                    setCreatedTab={setCreatedTab}
                    currentTrip={currentTrip}
                    setCurrentTrip={setCurrentTrip}
                    vessels={vessels}
                    fuelLogs={fuelLogs}
                    logBookStartDate={logBookStartDate}
                />
            ),
        },
        {
            id: 'complete-logbook',
            label: 'Complete log entry',
            component: logBookConfig && !imCrew && (
                <LogEntrySignOff
                    offline={offline}
                    logBookConfig={logBookConfig}
                    updateTripReport={updateTripReport}
                    signOff={signOff ? signOff[0] : false}
                    updateSignOff={updateSignOff}
                    fuel={fuel}
                    locked={locked || !edit_logBookEntry}
                    crewMembers={crewMembers}
                    vessel={vessel}
                    logBook={logbook}
                    masterTerm={client?.masterTerm ?? 'Master'}
                    prevComments={prevComments}
                    onUpdatePrevComments={(coms: any) => {
                        setPrevComments(coms)
                    }}
                    screen={isLargeScreen ? 'Desktop' : 'Mobile'}
                />
            ),
        },
    ]

    return (
        <>
            <Tabs
                value={tab || 'crew'}
                className="w-full grid"
                onValueChange={(value) => {
                    if (value !== tab) {
                        setTab(value)
                    }
                }}>
                <TabsList className="w-fit md:block hidden">
                    {tabItems.map((item) => (
                        <TabsTrigger key={item.id} value={item.id}>
                            {item.label}
                        </TabsTrigger>
                    ))}
                </TabsList>
                {tabItems.map((item) => (
                    <TabsContent key={item.id} value={item.id}>
                        {item.component}
                    </TabsContent>
                ))}
            </Tabs>

            {/* Additional tabs that are conditionally shown */}
            {tab === 'engineLog' && loaded && logbook && (
                <div className="hidden lg:block">
                    <EngineChecks
                        fuel={fuel}
                        updateFuel={updateFuel}
                        locked={locked || !edit_logBookEntry}
                        logEntrySections={logEntrySections}
                        logBookEntryID={logentryID}
                    />
                </div>
            )}
            {tab === 'compengineLog' && loaded && logbook && (
                <div className="grid grid-cols-1 items-center">
                    <ComprehensiveEngineLogs
                        logbookSection={logbook.logBookEntrySections.nodes}
                    />
                </div>
            )}
            {tab === 'supernumerary' && (
                <div className="hidden lg:block">
                    <CrewSupernumerary
                        logBookConfig={logBookConfig}
                        supernumerary={supernumerary}
                        setSupernumerary={setSupernumerary}
                        locked={locked || !edit_logBookEntry}
                    />
                </div>
            )}
            <div className="h-20" />
        </>
    )
}
