'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import {
    CreateEventType_BarCrossing,
    UpdateEventType_BarCrossing,
    CreateTripEvent,
    UpdateTripEvent,
} from '@/app/lib/graphQL/mutation'
import { BarCrossingChecklist, GetTripEvent } from '@/app/lib/graphQL/query'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import LocationField from '../components/location'
import TimeField from '../components/time'
import BarCrossingRiskAnalysis from './bar-crossing-risk-analysis'

import { getPermissions } from '@/app/helpers/userHelper'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_BarCrossingModel from '@/app/offline/models/eventType_BarCrossing'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { isBoolean } from 'lodash'

// UI Components
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import {
    Check,
    AlertCircle,
    SquareArrowOutUpRight,
    ArrowLeft,
} from 'lucide-react'
import { H4, P, RadioGroup } from '@/components/ui'
import { CheckFieldLabel } from '@/components/ui/check-field-label'

export default function BarCrossing({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    logBookConfig,
    members = false,
    locked,
    offline = false,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    logBookConfig: any
    members: any
    locked: any
    offline?: boolean
}) {
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [endTime, setEndTime] = useState<any>(dayjs().format('HH:mm'))
    const [content, setContent] = useState<any>('')
    const [barCrossing, setBarCrossing] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [barCrossingChecklistID, setBarCrossingChecklistID] = useState<any>(0)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [openRiskAnalysis, setOpenRiskAnalysis] = useState(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    const [permissions, setPermissions] = useState<any>(false)
    const tripEventModel = new TripEventModel()
    const barCrossingModel = new EventType_BarCrossingModel()
    const init_permissions = () => {
        // Check permissions for risk analysis editing if needed
        // Currently not used in this component
    }
    const [allChecked, setAllChecked] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const handleTimeChange = (date: any) => {
        setTime(dayjs(date).format('HH:mm'))
    }

    const handleEndTimeChange = (date: any) => {
        setEndTime(dayjs(date).format('HH:mm'))
    }

    useEffect(() => {
        setBarCrossing(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setBarCrossing(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const [getRiskAnalysis] = useLazyQuery(BarCrossingChecklist, {
        fetchPolicy: 'no-cache',
        onCompleted: (data) => {
            // Check if all fields are checked
            const checklist = data.readOneBarCrossingChecklist
            const numberOfFields = 7 // Number of fields from the fields constant in BarCrossingRiskAnalysis
            let counter = 0
            for (const item in checklist) {
                if (isBoolean(checklist[item])) {
                    if (checklist[item] === true) {
                        counter++
                    }
                }
            }
            setAllChecked(counter === numberOfFields)
        },
        onError: (error) => {
            console.error('BarCrossingChecklist onError', error)
        },
    })
    useEffect(() => {
        if (barCrossingChecklistID > 0) {
            getRiskAnalysis({
                variables: {
                    id: barCrossingChecklistID,
                },
            })
        }
    }, [barCrossingChecklistID])
    const getCurrentEvent = async (id: any) => {
        if (offline) {
            const event = await tripEventModel.getById(id)
            if (event) {
                if (!event.eventType_BarCrossing) {
                    const eventType_BarCrossing =
                        await barCrossingModel.getById(id)
                    event.eventType_BarCrossing = eventType_BarCrossing
                }
                setTripEvent(event)
                setBarCrossing({
                    geoLocationID: event.eventType_BarCrossing?.geoLocationID,
                    geoLocationCompletedID:
                        event.eventType_BarCrossing?.geoLocationCompletedID,
                    time: event.eventType_BarCrossing?.time,
                    timeCompleted: event.eventType_BarCrossing?.timeCompleted,
                    stopAssessPlan: event.eventType_BarCrossing?.stopAssessPlan,
                    crewBriefing: event.eventType_BarCrossing?.crewBriefing,
                    weather: event.eventType_BarCrossing?.weather,
                    stability: event.eventType_BarCrossing?.stability,
                    waterTightness: event.eventType_BarCrossing?.waterTightness,
                    lifeJackets: event.eventType_BarCrossing?.lifeJackets,
                    lookoutPosted: event.eventType_BarCrossing?.lookoutPosted,
                    report: event.eventType_BarCrossing?.report,
                    lat: event.eventType_BarCrossing?.lat,
                    long: event.eventType_BarCrossing?.long,
                    latCompleted: event.eventType_BarCrossing?.latCompleted,
                    longCompleted: event.eventType_BarCrossing?.longCompleted,
                })
                if (
                    event.eventType_BarCrossing?.lat &&
                    event.eventType_BarCrossing?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_BarCrossing?.lat,
                        longitude: event.eventType_BarCrossing?.long,
                    })
                }
                setContent(event.eventType_BarCrossing?.report)
                setTime(event.eventType_BarCrossing?.time)
                setEndTime(event.eventType_BarCrossing?.timeCompleted)
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setBarCrossing({
                    geoLocationID: event.eventType_BarCrossing?.geoLocationID,
                    geoLocationCompletedID:
                        event.eventType_BarCrossing?.geoLocationCompletedID,
                    time: event.eventType_BarCrossing?.time,
                    timeCompleted: event.eventType_BarCrossing?.timeCompleted,
                    stopAssessPlan: event.eventType_BarCrossing?.stopAssessPlan,
                    crewBriefing: event.eventType_BarCrossing?.crewBriefing,
                    weather: event.eventType_BarCrossing?.weather,
                    stability: event.eventType_BarCrossing?.stability,
                    waterTightness: event.eventType_BarCrossing?.waterTightness,
                    lifeJackets: event.eventType_BarCrossing?.lifeJackets,
                    lookoutPosted: event.eventType_BarCrossing?.lookoutPosted,
                    report: event.eventType_BarCrossing?.report,
                    lat: event.eventType_BarCrossing?.lat,
                    long: event.eventType_BarCrossing?.long,
                    latCompleted: event.eventType_BarCrossing?.latCompleted,
                    longCompleted: event.eventType_BarCrossing?.longCompleted,
                })
                if (
                    event.eventType_BarCrossing?.lat &&
                    event.eventType_BarCrossing?.long
                ) {
                    setCurrentLocation({
                        latitude: event.eventType_BarCrossing?.lat,
                        longitude: event.eventType_BarCrossing?.long,
                    })
                }
                setContent(event.eventType_BarCrossing?.report)
                setTime(event.eventType_BarCrossing?.time)
                setEndTime(event.eventType_BarCrossing?.timeCompleted)
                setBarCrossingChecklistID(
                    event.eventType_BarCrossing?.barCrossingChecklist.id,
                )
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                geoLocationID: barCrossing?.geoLocationID,
                geoLocationCompletedID: barCrossing?.geoLocationCompletedID,
                time: time,
                timeCompleted: endTime,
                stopAssessPlan: barCrossing?.stopAssessPlan,
                crewBriefing: barCrossing?.crewBriefing,
                weather: barCrossing?.weather,
                stability: barCrossing?.stability,
                waterTightness: barCrossing?.waterTightness,
                lifeJackets: barCrossing?.lifeJackets,
                lookoutPosted: barCrossing?.lookoutPosted,
                report: content,
                lat: currentLocation.latitude.toString(),
                long: currentLocation.longitude.toString(),
            },
        }
        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'BarCrossing',
                    logBookEntrySectionID: currentTrip.id,
                })
                await getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'BarCrossing',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            if (offline) {
                await barCrossingModel.save({
                    id: +selectedEvent?.eventType_BarCrossingID,
                    ...variables.input,
                })
            } else {
                updateEventType_BarCrossing({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_BarCrossingID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'BarCrossing',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(tripEventData)
                const barCrossingData = await barCrossingModel.save({
                    id: generateUniqueId(),
                    geoLocationID: barCrossing?.geoLocationID ?? 0,
                    geoLocationCompletedID:
                        barCrossing?.geoLocationCompletedID ?? 0,
                    time: time ?? null,
                    timeCompleted: endTime ?? null,
                    stopAssessPlan: barCrossing?.stopAssessPlan ?? false,
                    crewBriefing: barCrossing?.crewBriefing ?? false,
                    weather: barCrossing?.weather ?? false,
                    stability: barCrossing?.stability ?? false,
                    waterTightness: barCrossing?.waterTightness ?? false,
                    lifeJackets: barCrossing?.lifeJackets ?? false,
                    lookoutPosted: barCrossing?.lookoutPosted ?? false,
                    report: content ?? null,
                    lat: currentLocation.latitude.toString() ?? null,
                    long: currentLocation.longitude.toString() ?? null,
                    barCrossingChecklistID: barCrossingChecklistID,
                })
                await tripEventModel.save({
                    id: tripEventData.id,
                    eventType_BarCrossingID: barCrossingData.id,
                })
                await getCurrentEvent(tripEventData.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                closeModal()
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'BarCrossing',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            setCurrentEvent(data)
            createEventType_BarCrossing({
                variables: {
                    input: {
                        geoLocationID: barCrossing?.geoLocationID,
                        geoLocationCompletedID:
                            barCrossing?.geoLocationCompletedID,
                        time: time,
                        timeCompleted: endTime,
                        stopAssessPlan: barCrossing?.stopAssessPlan,
                        crewBriefing: barCrossing?.crewBriefing,
                        weather: barCrossing?.weather,
                        stability: barCrossing?.stability,
                        waterTightness: barCrossing?.waterTightness,
                        lifeJackets: barCrossing?.lifeJackets,
                        lookoutPosted: barCrossing?.lookoutPosted,
                        report: content,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                        barCrossingChecklistID: barCrossingChecklistID,
                    },
                },
            })
            updateTripEvent({
                variables: {
                    input: {
                        id: data.id,
                        eventCategory: 'BarCrossing',
                        eventType_BarCrossingID: currentTrip.id,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_BarCrossing] = useMutation(
        CreateEventType_BarCrossing,
        {
            onCompleted: (response) => {
                const data = response.createEventType_BarCrossing
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEvent?.id,
                            eventType_BarCrossingID: data.id,
                        },
                    },
                })
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating bar crossing', error)
            },
        },
    )

    const [updateEventType_BarCrossing] = useMutation(
        UpdateEventType_BarCrossing,
        {
            onCompleted: () => {
                // Successfully updated bar crossing
            },
            onError: (error) => {
                console.error('Error updating bar crossing', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: () => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )

        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, clear the location
        if (!value) {
            setBarCrossing({
                ...barCrossing,
                geoLocationID: 0,
                lat: null,
                long: null,
            })
            return
        }

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setBarCrossing({
                ...barCrossing,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })

            // If the value object has latitude and longitude, update currentLocation
            if (value.latitude !== undefined && value.longitude !== undefined) {
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setBarCrossing({
                ...barCrossing,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })

            // Update currentLocation
            setCurrentLocation({
                latitude: value.latitude,
                longitude: value.longitude,
            })
        }
    }

    // Function to handle end location changes if needed in the future
    // const handleEndLocationChange = (value: any) => {
    //     setBarCrossing({
    //         ...barCrossing,
    //         geoLocationCompletedID: value?.value,
    //     })
    // }

    return (
        <div className="w-full space-y-8">
            {displayField('BarCrossing_Location') ||
            displayField('BarCrossing_Time') ? (
                <div className="space-y-8">
                    <Label
                        label="Location of bar crossing"
                        htmlFor="bar-crossing-location"
                        className={`${locked ? 'pointer-events-none' : ''}`}>
                        {displayField('BarCrossing_Location') && (
                            <LocationField
                                offline={offline}
                                setCurrentLocation={setCurrentLocation}
                                handleLocationChange={handleLocationChange}
                                currentEvent={tripEvent.eventType_BarCrossing}
                            />
                        )}
                    </Label>
                    {displayField('BarCrossing_Time') && (
                        <Label
                            label="Times when bar crossing starts"
                            htmlFor="crossing-time"
                            disabled={locked}>
                            <TimeField
                                time={time}
                                handleTimeChange={handleTimeChange}
                                timeID="crossing-time"
                                fieldName="Time"
                                buttonLabel="Set to now"
                            />
                        </Label>
                    )}

                    <CheckFieldLabel
                        type="checkbox"
                        checked={allChecked}
                        className="w-fit"
                        variant="success"
                        leftContent={<AlertCircle className="h-4 w-4" />}
                        rightContent={
                            <SquareArrowOutUpRight className="h-4 w-4" />
                        }
                        onClick={() => {
                            setOpenRiskAnalysis(true)
                        }}
                        label="Bar crossing - risk analysis"
                    />
                </div>
            ) : null}
            {displayField('BarCrossing_Report') && (
                <div>
                    <Label>Crossing comments</Label>
                    <div className={`${locked ? 'pointer-events-none' : ''}`}>
                        {(!currentEvent || barCrossing) && (
                            <Editor
                                id="bar-crossing-report"
                                placeholder="Bar crossing report - record any comment associated with the crossing"
                                className="w-full ring-1 ring-inset"
                                content={content}
                                handleEditorChange={handleEditorChange}
                            />
                        )}
                    </div>
                </div>
            )}
            {displayField('BarCrossing_EndTime') ? (
                <>
                    {displayField('BarCrossing_EndTime') && (
                        <Label
                            label="Times when bar crossing ends"
                            htmlFor="end-crossing-time"
                            className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                            <TimeField
                                time={endTime}
                                handleTimeChange={handleEndTimeChange}
                                timeID="end-crossing-time"
                                fieldName="Time"
                                buttonLabel="Set to now"
                            />
                        </Label>
                    )}
                </>
            ) : (
                ''
            )}
            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    onClick={() => closeModal()}
                    iconLeft={ArrowLeft}>
                    Cancel
                </Button>
                <Button
                    onClick={locked ? () => {} : handleSave}
                    disabled={locked}
                    iconLeft={Check}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>

            <BarCrossingRiskAnalysis
                offline={offline}
                selectedEvent={selectedEvent}
                onSidebarClose={() => setOpenRiskAnalysis(false)}
                logBookConfig={logBookConfig}
                currentTrip={currentTrip}
                crewMembers={members}
                barCrossingChecklistID={barCrossingChecklistID}
                setBarCrossingChecklistID={setBarCrossingChecklistID}
                setAllChecked={setAllChecked}
                open={openRiskAnalysis}
                onOpenChange={setOpenRiskAnalysis}
            />
        </div>
    )
}
