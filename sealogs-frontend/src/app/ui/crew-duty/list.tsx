'use client'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { List, TableSkeleton } from '@/components/skeletons'
import { getCrewDuties } from '@/app/lib/actions'
import { preventCrewAccess } from '@/app/helpers/userHelper'
import { useRouter } from 'next/navigation'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'
import { H2 } from '@/components/ui'

const CrewDutyList = () => {
    const router = useRouter()
    const [crewDuties, setCrewDuties] = useState([])

    const handleSetDuties = (duties: any) => {
        const activeDuties = duties.filter((duty: any) => !duty.Archived)
        setCrewDuties(activeDuties)
    }
    getCrewDuties(handleSetDuties)

    useEffect(() => {
        preventCrewAccess()
    }, [])
    return (
        <div className="w-full p-0">
            <div className="flex justify-between items-center">
                <H2 className="text-3xl">Crew Duties</H2>
                <SeaLogsButton
                    // link={`/settings/crew-duty/create`}
                    action={() => router.push(`/settings/crew-duty/create`)}
                    text="New Crew Duty"
                    color="sllightblue"
                    type="primary"
                />
            </div>
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {!crewDuties ? (
                        <TableSkeleton />
                    ) : (
                        <TableWrapper
                            headings={['Title:firstHead', 'Abbreviation']}>
                            {crewDuties.map((crewDuty: any) => (
                                <tr
                                    key={crewDuty.id}
                                    className={`group border-b  hover:   `}>
                                    <td className="pl-2 py-3 lg:px-6 text-left">
                                        <Link
                                            href={`/settings/crew-duty/edit?id=${crewDuty.id}`}
                                            className="">
                                            {crewDuty.title}
                                        </Link>
                                    </td>
                                    <td className="px-2">
                                        {crewDuty.abbreviation}
                                    </td>
                                </tr>
                            ))}
                        </TableWrapper>
                    )}
                </div>
            </div>
        </div>
    )
}

export default CrewDutyList
